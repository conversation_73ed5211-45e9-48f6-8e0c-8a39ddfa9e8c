import React from "react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  FileText,
  FolderOpen,
  Settings,
  ThumbsUp,
  Users,
  BookText,
  LayoutDashboard,
  UserCircle,
  ChevronDown,
  Loader2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Sidebar as ShadcnSidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { useOptimizedWorkspaceSwitching } from "@/hooks/useOptimizedWorkspaceSwitching";
import { Workspace } from "@/types/workspace";
import {
  WorkspaceSwitcherLoading,
  WorkspaceSwitcherError,
  WorkspaceSwitcherEmpty,
} from "@/components/workspace/WorkspaceLoadingStates";

interface SidebarProps {
  activePath: string;
  userName: string;
  userRole: string;
  currentWorkspace?: Workspace;
  onWorkspaceChange?: (workspace: Workspace) => void;
}

const Sidebar = ({
  activePath,
  userName,
  userRole,
  currentWorkspace,
  onWorkspaceChange = () => {},
}: SidebarProps) => {
  // Get workspaces from ClerkWorkspaceProvider instead of fetching directly
  const { getUserWorkspaces, isLoading: workspacesLoading } = useClerkWorkspace();
  const workspaces = getUserWorkspaces();
  const loading = workspacesLoading;
  const error = null; // ClerkWorkspaceProvider handles errors internally

  // Optimized workspace switching
  const {
    switchWorkspaceImmediate,
    preloadWorkspace,
    isLoading: isSwitching,
    isTransitioning,
    error: switchError,
    clearError,
  } = useOptimizedWorkspaceSwitching({
    onWorkspaceChanged: (workspaceId) => {
      // Find the workspace and call the original handler
      const workspace = workspaces.find(ws => ws.id === workspaceId);
      if (workspace) {
        onWorkspaceChange(workspace);
      }
    },
    onError: (error, workspaceId) => {
      console.error(`Failed to switch to workspace ${workspaceId}:`, error);
    },
    enablePreloading: true,
    enableOptimisticUpdates: true,
    debounceMs: 200,
  });

  // Handle workspace click with optimized switching
  const handleWorkspaceClick = (workspace: Workspace) => {
    if (workspace.id === currentWorkspace?.id) return; // Already active

    // Use immediate switch for better UX
    switchWorkspaceImmediate(workspace.id, workspace.name);
  };

  // Preload workspace data on hover
  const handleWorkspaceHover = (workspace: Workspace) => {
    if (workspace.id !== currentWorkspace?.id) {
      preloadWorkspace(workspace.id);
    }
  };

  // Navigation items configuration
  const navigationItems = [
    {
      title: "Dashboard",
      url: "/app/dashboard",
      icon: LayoutDashboard,
      isActive: activePath === "/app/dashboard",
    },
    {
      title: "Contracts",
      url: "/app/contracts",
      icon: FileText,
      isActive: activePath?.startsWith("/app/contracts"),
    },
    {
      title: "Repository",
      url: "/app/repository",
      icon: FolderOpen,
      isActive: activePath?.startsWith("/app/repository"),
    },
    {
      title: "Approvals",
      url: "/app/approvals",
      icon: ThumbsUp,
      isActive: activePath?.startsWith("/app/approvals"),
      badge: "3",
    },
  ];

  const toolsItems = [
    {
      title: "Analytics",
      url: "/app/analytics",
      icon: BarChart3,
      isActive: activePath?.startsWith("/app/analytics"),
    },
    {
      title: "Workspaces",
      url: "/app/workspaces",
      icon: Users,
      isActive: activePath?.startsWith("/app/workspaces"),
    },
    {
      title: "Clause Library",
      url: "/app/clause-library",
      icon: BookText,
      isActive: activePath?.startsWith("/app/clause-library"),
    },
  ];

  return (
    <ShadcnSidebar variant="sidebar" collapsible="icon" className="w-64 border-r shadow-sm">
      <SidebarHeader className="p-4">
        <div className="flex items-center gap-2.5">
          <div className="h-9 w-9 rounded-md bg-primary flex items-center justify-center shadow-sm">
            <span className="text-lg font-bold text-primary-foreground">A</span>
          </div>
          <span className="text-xl font-bold tracking-tight group-data-[collapsible=icon]:hidden">
            Averum
          </span>
        </div>
      </SidebarHeader>

      <SidebarSeparator className="opacity-70" />

      <SidebarContent>
        <SidebarGroup>
          <div className="px-2 pb-2">
            <DropdownMenu>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full gap-2 justify-between transition-all duration-200",
                        "group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-2",
                        isTransitioning && "scale-[0.98] opacity-80",
                        isSwitching && "cursor-wait"
                      )}
                      disabled={isSwitching}
                    >
                      <div className="flex items-center gap-2 group-data-[collapsible=icon]:gap-0">
                        <div className={cn(
                          "h-5 w-5 rounded-full bg-primary flex items-center justify-center transition-all duration-200",
                          isTransitioning && "animate-pulse"
                        )}>
                          {isSwitching ? (
                            <Loader2 className="h-3 w-3 animate-spin text-primary-foreground" />
                          ) : (
                            <span className="text-[10px] font-medium text-primary-foreground">
                              {currentWorkspace?.name
                                ? currentWorkspace.name.charAt(0)
                                : "L"}
                            </span>
                          )}
                        </div>
                        <span className={cn(
                          "font-medium text-sm transition-all duration-200 group-data-[collapsible=icon]:hidden",
                          isTransitioning && "opacity-70"
                        )}>
                          {currentWorkspace?.name || "Workspace"}
                        </span>
                      </div>
                      <ChevronDown className={cn(
                        "h-4 w-4 opacity-50 transition-transform duration-200 group-data-[collapsible=icon]:hidden",
                        isSwitching && "animate-spin"
                      )} />
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent side="right" className="group-data-[state=expanded]:hidden">
                  <p>{currentWorkspace?.name || "Switch Workspace"}</p>
                </TooltipContent>
              </Tooltip>
              <DropdownMenuContent align="start" className="w-[220px] p-0">
                {/* Fixed Header */}
                <div className="px-2 py-1.5 border-b border-border/50">
                  <DropdownMenuLabel className="px-0 py-1">Workspaces</DropdownMenuLabel>
                </div>

                {/* Scrollable Workspace List */}
                <div className="max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border/50 hover:scrollbar-thumb-border/80">
                  {switchError && (
                    <div className="px-2 py-2 mx-1 mb-1 bg-destructive/10 border border-destructive/20 rounded-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-destructive">{switchError}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 text-destructive hover:text-destructive"
                          onClick={clearError}
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  )}
                  {loading ? (
                    <WorkspaceSwitcherLoading message="Loading workspaces..." />
                  ) : error ? (
                    <WorkspaceSwitcherError
                      error={error}
                      onRetry={() => window.location.reload()}
                    />
                  ) : workspaces.length === 0 ? (
                    <WorkspaceSwitcherEmpty
                      onCreateWorkspace={() => (window.location.href = "/app/workspaces")}
                    />
                  ) : (
                    <div className="py-1">
                      {workspaces.map((workspace) => (
                        <DropdownMenuItem
                          key={workspace.id}
                          className={cn(
                            "flex items-center gap-2 py-2 mx-1 rounded-sm focus:bg-accent/50 cursor-pointer transition-all duration-150",
                            workspace.isActive && "bg-accent/30",
                            isSwitching && workspace.id === currentWorkspace?.id && "opacity-50"
                          )}
                          onClick={() => handleWorkspaceClick(workspace)}
                          onMouseEnter={() => handleWorkspaceHover(workspace)}
                          disabled={isSwitching}
                        >
                          <div className={cn(
                            "h-5 w-5 rounded-full bg-primary flex items-center justify-center flex-shrink-0 transition-all duration-150",
                            workspace.isActive && "ring-2 ring-primary/20"
                          )}>
                            <span className="text-[10px] font-medium text-primary-foreground">
                              {workspace?.name ? workspace.name.charAt(0) : "W"}
                            </span>
                          </div>
                          <div className="flex flex-col min-w-0 flex-1">
                            <span className={cn(
                              "text-sm font-medium truncate transition-all duration-150",
                              workspace.isActive && "text-primary"
                            )}>
                              {workspace.name}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {workspace.members} member{workspace.members !== 1 ? 's' : ''}
                            </span>
                          </div>
                          {workspace.isActive && (
                            <Badge variant="outline" className="ml-auto text-xs flex-shrink-0 border-primary/50 text-primary">
                              Active
                            </Badge>
                          )}
                          {isSwitching && workspace.id === currentWorkspace?.id && (
                            <Loader2 className="ml-auto h-3 w-3 animate-spin text-muted-foreground" />
                          )}
                        </DropdownMenuItem>
                      ))}
                    </div>
                  )}
                </div>

                {/* Fixed Footer */}
                {workspaces.length > 0 && (
                  <div className="border-t border-border/50 p-1">
                    <DropdownMenuItem
                      onClick={() => (window.location.href = "/app/workspaces")}
                      className="mx-0 rounded-sm focus:bg-accent/50 cursor-pointer"
                    >
                      <span className="text-sm">Manage Workspaces</span>
                    </DropdownMenuItem>
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>MAIN NAVIGATION</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={item.isActive}
                    tooltip={item.title}
                    className={cn(
                      item.isActive && "bg-sidebar-accent text-sidebar-accent-foreground font-semibold"
                    )}
                  >
                    <Link to={item.url}>
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                      {item.badge && (
                        <Badge
                          variant="outline"
                          className={cn(
                            "ml-auto text-xs",
                            item.isActive
                              ? "bg-white/20 text-white border-white/30"
                              : "bg-primary/10 text-primary border-primary/30"
                          )}
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>TOOLS & RESOURCES</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {toolsItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={item.isActive}
                    tooltip={item.title}
                    className={cn(
                      item.isActive && "bg-sidebar-accent text-sidebar-accent-foreground font-semibold"
                    )}
                  >
                    <Link to={item.url}>
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarSeparator className="opacity-70" />

      <SidebarFooter className="p-3 space-y-1">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={activePath === "/app/settings"}
              tooltip="Settings"
              className={cn(
                activePath === "/app/settings" && "bg-sidebar-accent text-sidebar-accent-foreground font-semibold"
              )}
            >
              <Link to="/app/settings">
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        <div className="mt-3 pt-3 border-t border-border/50">
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-2.5 p-2 rounded-md group-data-[collapsible=icon]:justify-center cursor-default">
                <div className="h-8 w-8 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center">
                  <UserCircle className="h-5 w-5 text-primary/80" />
                </div>
                <div className="flex flex-col items-start group-data-[collapsible=icon]:hidden">
                  <span className="text-sm font-medium">{userName}</span>
                  <span className="text-xs text-muted-foreground">{userRole}</span>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right" className="group-data-[state=expanded]:hidden">
              <div className="text-center">
                <p className="font-medium">{userName}</p>
                <p className="text-xs text-muted-foreground">{userRole}</p>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </SidebarFooter>

      {/* Sidebar Rail - provides the collapse/expand functionality */}
      <SidebarRail />
    </ShadcnSidebar>
  );
};

export default Sidebar;
