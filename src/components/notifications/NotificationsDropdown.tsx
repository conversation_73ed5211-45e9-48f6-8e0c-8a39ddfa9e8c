import React from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { Bell } from "lucide-react";
import EnhancedNotificationsCenter from "./EnhancedNotificationsCenter";
import { useNotifications } from "@/hooks/useNotifications";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";

interface NotificationsDropdownProps {
  onSettingsClick?: () => void;
}

const NotificationsDropdown: React.FC<NotificationsDropdownProps> = ({
  onSettingsClick,
}) => {
  const { currentWorkspace } = useClerkWorkspace();
  const { summary } = useNotifications({
    workspaceId: currentWorkspace?.id || '',
    autoRefresh: true,
    refreshInterval: 30000
  });

  const unreadCount = summary?.unread_count || 0;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-muted/50 transition-colors"
        >
          <Bell className="h-5 w-5 opacity-70 hover:opacity-100 transition-opacity" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-destructive text-[10px] font-medium text-destructive-foreground flex items-center justify-center shadow-sm">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[380px] p-0 mr-4"
        align="end"
        sideOffset={16}
      >
        <div className="max-h-[80vh] overflow-hidden">
          <EnhancedNotificationsCenter
            onSettingsClick={onSettingsClick}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default NotificationsDropdown;
