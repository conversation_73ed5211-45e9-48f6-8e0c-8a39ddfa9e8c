"""
Comprehensive data seeding script for LegalAI V4
Creates realistic sample data for contracts, templates, workspaces, and users
"""

import os
import sys
import uuid
import json
from datetime import datetime, date, timedelta
from typing import List, Dict, Any
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from supabase import create_client, Client

class DataSeeder:
    def __init__(self):
        """Initialize the data seeder with Supabase client."""
        self.supabase = self._get_supabase_client()

    def _get_supabase_client(self) -> Client:
        """Get Supabase client from environment variables."""
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")

        if not supabase_url or not supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables must be set")

        return create_client(supabase_url, supabase_key)

    def clear_existing_data(self):
        """Clear existing sample data (optional - for development)."""
        print("🧹 Clearing existing sample data...")

        # Clear in reverse dependency order
        tables_to_clear = [
            "ai_analysis_results",
            "export_history",
            "activity_logs",
            "notifications",
            "contract_signers",
            "document_signers",
            "documents",
            "contracts",
            "templates",
            "clauses",
            "folders",
            "workspace_members",
            "roles",
            "workspaces",
            "users",
            "permissions"
        ]

        for table in tables_to_clear:
            try:
                # Only clear demo/sample data, not all data
                if table == "users":
                    self.supabase.table(table).delete().like("email", "%@demo.averumcontracts.com").execute()
                elif table == "workspaces":
                    self.supabase.table(table).delete().like("name", "Demo %").execute()
                else:
                    # For other tables, we'll rely on cascade deletes
                    pass
                print(f"   ✅ Cleared sample data from {table}")
            except Exception as e:
                print(f"   ⚠️  Warning: Could not clear {table}: {e}")

    def seed_demo_users(self) -> List[Dict[str, Any]]:
        """Create demo users for different roles and industries."""
        print("👥 Creating demo users...")

        demo_users = [
            {
                "id": "demo-admin-001",
                "email": "<EMAIL>",
                "first_name": "Sarah",
                "last_name": "Johnson",
                "title": "Legal Operations Manager",
                "company": "TechCorp Inc.",
                "timezone": "America/New_York",
                "bio": "Experienced legal operations professional specializing in contract management and compliance.",
                "initials": "SJ",
                "notification_preferences": {
                    "email_approvals": True,
                    "email_updates": True,
                    "email_reminders": True,
                    "email_comments": True,
                    "system_approvals": True,
                    "browser_notifications": True,
                    "email_digest_frequency": "daily"
                },
                "workspaces": [],
                "workspace_roles": {}
            },
            {
                "id": "demo-lawyer-001",
                "email": "<EMAIL>",
                "first_name": "Michael",
                "last_name": "Chen",
                "title": "Senior Legal Counsel",
                "company": "TechCorp Inc.",
                "timezone": "America/Los_Angeles",
                "bio": "Corporate lawyer with 10+ years experience in technology contracts and intellectual property.",
                "initials": "MC",
                "notification_preferences": {
                    "email_approvals": True,
                    "email_updates": True,
                    "email_reminders": True,
                    "email_comments": True,
                    "system_approvals": True,
                    "browser_notifications": True,
                    "email_digest_frequency": "daily"
                },
                "workspaces": [],
                "workspace_roles": {}
            },
            {
                "id": "demo-manager-001",
                "email": "<EMAIL>",
                "first_name": "Emily",
                "last_name": "Rodriguez",
                "title": "Business Development Manager",
                "company": "TechCorp Inc.",
                "timezone": "America/Chicago",
                "bio": "Business development professional focused on strategic partnerships and vendor relationships.",
                "initials": "ER",
                "notification_preferences": {
                    "email_approvals": True,
                    "email_updates": False,
                    "email_reminders": True,
                    "email_comments": False,
                    "system_approvals": True,
                    "browser_notifications": False,
                    "email_digest_frequency": "weekly"
                },
                "workspaces": [],
                "workspace_roles": {}
            },
            {
                "id": "demo-contractor-001",
                "email": "<EMAIL>",
                "first_name": "David",
                "last_name": "Kim",
                "title": "Independent Consultant",
                "company": "Kim Consulting LLC",
                "timezone": "America/Denver",
                "bio": "Technology consultant specializing in software development and system integration.",
                "initials": "DK",
                "notification_preferences": {
                    "email_approvals": True,
                    "email_updates": True,
                    "email_reminders": True,
                    "email_comments": True,
                    "system_approvals": True,
                    "browser_notifications": True,
                    "email_digest_frequency": "daily"
                },
                "workspaces": [],
                "workspace_roles": {}
            },
            {
                "id": "demo-hr-001",
                "email": "<EMAIL>",
                "first_name": "Lisa",
                "last_name": "Thompson",
                "title": "HR Director",
                "company": "TechCorp Inc.",
                "timezone": "America/New_York",
                "bio": "Human resources professional with expertise in employment law and organizational development.",
                "initials": "LT",
                "notification_preferences": {
                    "email_approvals": True,
                    "email_updates": True,
                    "email_reminders": True,
                    "email_comments": True,
                    "system_approvals": True,
                    "browser_notifications": True,
                    "email_digest_frequency": "daily"
                },
                "workspaces": [],
                "workspace_roles": {}
            }
        ]

        created_users = []
        for user_data in demo_users:
            try:
                response = self.supabase.table("users").insert(user_data).execute()
                if response.data:
                    created_users.append(response.data[0])
                    print(f"   ✅ Created user: {user_data['first_name']} {user_data['last_name']}")
            except Exception as e:
                print(f"   ⚠️  Warning: Could not create user {user_data['email']}: {e}")
                # If user already exists, fetch it
                try:
                    existing_response = self.supabase.table("users").select("*").eq("email", user_data["email"]).execute()
                    if existing_response.data:
                        created_users.append(existing_response.data[0])
                        print(f"   ✅ Using existing user: {user_data['email']}")
                except Exception as fetch_error:
                    print(f"   ❌ Could not fetch existing user {user_data['email']}: {fetch_error}")

        return created_users

    def seed_default_permissions(self) -> List[Dict[str, Any]]:
        """Create default system permissions."""
        print("🔐 Creating default permissions...")

        default_permissions = [
            {"id": "perm-1", "name": "contracts.view", "description": "View contracts", "category": "contracts"},
            {"id": "perm-2", "name": "contracts.create", "description": "Create contracts", "category": "contracts"},
            {"id": "perm-3", "name": "contracts.edit", "description": "Edit contracts", "category": "contracts"},
            {"id": "perm-4", "name": "contracts.delete", "description": "Delete contracts", "category": "contracts"},
            {"id": "perm-5", "name": "contracts.approve", "description": "Approve contracts", "category": "contracts"},
            {"id": "perm-6", "name": "templates.view", "description": "View templates", "category": "templates"},
            {"id": "perm-7", "name": "templates.create", "description": "Create templates", "category": "templates"},
            {"id": "perm-8", "name": "templates.edit", "description": "Edit templates", "category": "templates"},
            {"id": "perm-9", "name": "templates.delete", "description": "Delete templates", "category": "templates"},
            {"id": "perm-10", "name": "clauses.view", "description": "View clauses", "category": "clauses"},
            {"id": "perm-11", "name": "clauses.create", "description": "Create clauses", "category": "clauses"},
            {"id": "perm-12", "name": "clauses.edit", "description": "Edit clauses", "category": "clauses"},
            {"id": "perm-13", "name": "clauses.delete", "description": "Delete clauses", "category": "clauses"},
            {"id": "perm-14", "name": "users.manage", "description": "Manage users", "category": "users"},
            {"id": "perm-15", "name": "analytics.view", "description": "View analytics", "category": "analytics"},
            {"id": "perm-16", "name": "workspace.admin", "description": "Workspace administration", "category": "workspace"},
        ]

        created_permissions = []
        for permission_data in default_permissions:
            try:
                response = self.supabase.table("permissions").insert(permission_data).execute()
                if response.data:
                    created_permissions.append(response.data[0])
                    print(f"   ✅ Created permission: {permission_data['name']}")
            except Exception as e:
                print(f"   ⚠️  Warning: Could not create permission {permission_data['name']}: {e}")

        return created_permissions

    def seed_demo_workspaces(self, users: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create demo workspaces for different industries."""
        print("🏢 Creating demo workspaces...")

        demo_workspaces = [
            {
                "id": "demo-workspace-tech",
                "name": "Demo Tech Workspace",
                "description": "Technology company workspace for software development contracts, NDAs, and vendor agreements.",
                "created_by": "demo-admin-001",
                "members": 0,
                "contracts": 0,
                "is_active": True
            },
            {
                "id": "demo-workspace-legal",
                "name": "Demo Legal Workspace",
                "description": "Legal department workspace for managing all corporate contracts and compliance documents.",
                "created_by": "demo-lawyer-001",
                "members": 0,
                "contracts": 0,
                "is_active": True
            },
            {
                "id": "demo-workspace-hr",
                "name": "Demo HR Workspace",
                "description": "Human resources workspace for employment contracts, NDAs, and HR-related agreements.",
                "created_by": "demo-hr-001",
                "members": 0,
                "contracts": 0,
                "is_active": True
            },
            {
                "id": "demo-workspace-consulting",
                "name": "Demo Consulting Workspace",
                "description": "Independent consulting workspace for client contracts and service agreements.",
                "created_by": "demo-contractor-001",
                "members": 0,
                "contracts": 0,
                "is_active": True
            }
        ]

        created_workspaces = []
        for workspace_data in demo_workspaces:
            try:
                response = self.supabase.table("workspaces").insert(workspace_data).execute()
                if response.data:
                    created_workspaces.append(response.data[0])
                    print(f"   ✅ Created workspace: {workspace_data['name']}")
            except Exception as e:
                print(f"   ⚠️  Warning: Could not create workspace {workspace_data['name']}: {e}")
                # If workspace already exists, fetch it
                try:
                    existing_response = self.supabase.table("workspaces").select("*").eq("id", workspace_data["id"]).execute()
                    if existing_response.data:
                        created_workspaces.append(existing_response.data[0])
                        print(f"   ✅ Using existing workspace: {workspace_data['name']}")
                except Exception as fetch_error:
                    print(f"   ❌ Could not fetch existing workspace {workspace_data['name']}: {fetch_error}")

        return created_workspaces

    def seed_default_roles(self, workspaces: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create default roles for each workspace."""
        print("👥 Creating default roles...")

        default_role_templates = [
            {
                "name": "Administrator",
                "description": "Full system access",
                "permissions": ["perm-1", "perm-2", "perm-3", "perm-4", "perm-5", "perm-6", "perm-7", "perm-8", "perm-9", "perm-10", "perm-11", "perm-12", "perm-13", "perm-14", "perm-15", "perm-16"],
                "is_system": True,
            },
            {
                "name": "Contract Manager",
                "description": "Manage contracts and templates",
                "permissions": ["perm-1", "perm-2", "perm-3", "perm-4", "perm-5", "perm-6", "perm-7", "perm-8", "perm-9", "perm-10", "perm-15"],
                "is_system": True,
            },
            {
                "name": "Legal Reviewer",
                "description": "Review and approve contracts",
                "permissions": ["perm-1", "perm-3", "perm-5", "perm-6", "perm-9"],
                "is_system": True,
            },
            {
                "name": "Read Only",
                "description": "View-only access",
                "permissions": ["perm-1", "perm-6", "perm-9", "perm-15"],
                "is_system": True,
            },
        ]

        created_roles = []
        for workspace in workspaces:
            for role_template in default_role_templates:
                role_data = {
                    "id": f"role-{role_template['name'].lower().replace(' ', '-')}-{workspace['id']}",
                    "name": role_template["name"],
                    "description": role_template["description"],
                    "permissions": role_template["permissions"],
                    "is_system": role_template["is_system"],
                    "workspace_id": workspace["id"]
                }

                try:
                    response = self.supabase.table("roles").insert(role_data).execute()
                    if response.data:
                        created_roles.append(response.data[0])
                        print(f"   ✅ Created role: {role_data['name']} for {workspace['name']}")
                except Exception as e:
                    print(f"   ⚠️  Warning: Could not create role {role_data['name']}: {e}")

        return created_roles

    def seed_workspace_members(self, users: List[Dict[str, Any]], workspaces: List[Dict[str, Any]]):
        """Add users to workspaces with appropriate roles."""
        print("👥 Adding users to workspaces...")

        # Define workspace memberships
        memberships = [
            # Tech Workspace
            {"workspace_id": "demo-workspace-tech", "user_id": "demo-admin-001", "role_id": "role-administrator-demo-workspace-tech"},
            {"workspace_id": "demo-workspace-tech", "user_id": "demo-lawyer-001", "role_id": "role-contract-manager-demo-workspace-tech"},
            {"workspace_id": "demo-workspace-tech", "user_id": "demo-manager-001", "role_id": "role-contract-manager-demo-workspace-tech"},
            {"workspace_id": "demo-workspace-tech", "user_id": "demo-contractor-001", "role_id": "role-read-only-demo-workspace-tech"},

            # Legal Workspace
            {"workspace_id": "demo-workspace-legal", "user_id": "demo-lawyer-001", "role_id": "role-administrator-demo-workspace-legal"},
            {"workspace_id": "demo-workspace-legal", "user_id": "demo-admin-001", "role_id": "role-contract-manager-demo-workspace-legal"},
            {"workspace_id": "demo-workspace-legal", "user_id": "demo-hr-001", "role_id": "role-legal-reviewer-demo-workspace-legal"},

            # HR Workspace
            {"workspace_id": "demo-workspace-hr", "user_id": "demo-hr-001", "role_id": "role-administrator-demo-workspace-hr"},
            {"workspace_id": "demo-workspace-hr", "user_id": "demo-admin-001", "role_id": "role-contract-manager-demo-workspace-hr"},
            {"workspace_id": "demo-workspace-hr", "user_id": "demo-lawyer-001", "role_id": "role-legal-reviewer-demo-workspace-hr"},

            # Consulting Workspace
            {"workspace_id": "demo-workspace-consulting", "user_id": "demo-contractor-001", "role_id": "role-administrator-demo-workspace-consulting"},
            {"workspace_id": "demo-workspace-consulting", "user_id": "demo-manager-001", "role_id": "role-read-only-demo-workspace-consulting"},
        ]

        for membership in memberships:
            try:
                response = self.supabase.table("workspace_members").insert(membership).execute()
                if response.data:
                    print(f"   ✅ Added user {membership['user_id']} to {membership['workspace_id']} as {membership['role_id']}")
            except Exception as e:
                print(f"   ⚠️  Warning: Could not add membership: {e}")

    def seed_demo_templates(self, workspaces: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create demo templates for different contract types."""
        print("📄 Creating demo templates...")

        demo_templates = [
            {
                "id": "demo-template-nda-001",
                "title": "Standard Non-Disclosure Agreement",
                "description": "Comprehensive NDA template for protecting confidential information in business discussions.",
                "type": "nda",
                "complexity": "medium",
                "industry": "Technology",
                "tags": ["confidentiality", "business", "legal"],
                "icon": "shield",
                "created_by": {
                    "id": "demo-lawyer-001",
                    "name": "Michael Chen"
                },
                "usage_count": 15,
                "rating": 4.8,
                "is_user_created": False,
                "workspace_id": "demo-workspace-legal",
                "content": {
                    "sections": [
                        {
                            "title": "Parties",
                            "content": "This Agreement is between [PARTY_1_NAME] and [PARTY_2_NAME]."
                        },
                        {
                            "title": "Definition of Confidential Information",
                            "content": "Confidential Information includes all technical data, trade secrets, know-how, research, product plans, products, services, customers, customer lists, markets, software, developments, inventions, processes, formulas, technology, designs, drawings, engineering, hardware configuration information, marketing, finances, or other business information."
                        },
                        {
                            "title": "Obligations",
                            "content": "The Receiving Party agrees to hold and maintain the Confidential Information in strict confidence and not to disclose such information to any third parties without prior written consent."
                        }
                    ],
                    "clauses": [
                        {
                            "id": "confidentiality",
                            "title": "Confidentiality Obligations",
                            "content": "The Receiving Party shall not disclose any Confidential Information to third parties."
                        },
                        {
                            "id": "term",
                            "title": "Term",
                            "content": "This Agreement shall remain in effect for [DURATION] years."
                        }
                    ]
                }
            },
            {
                "id": "demo-template-service-001",
                "title": "Professional Services Agreement",
                "description": "Comprehensive template for service provider agreements with detailed scope and payment terms.",
                "type": "service",
                "complexity": "complex",
                "industry": "Technology",
                "tags": ["services", "consulting", "professional"],
                "icon": "briefcase",
                "created_by": {
                    "id": "demo-admin-001",
                    "name": "Sarah Johnson"
                },
                "usage_count": 23,
                "rating": 4.6,
                "is_user_created": False,
                "workspace_id": "demo-workspace-tech",
                "content": {
                    "sections": [
                        {
                            "title": "Scope of Work",
                            "content": "The Service Provider shall provide [SERVICE_DESCRIPTION] in accordance with the specifications outlined in Exhibit A."
                        },
                        {
                            "title": "Payment Terms",
                            "content": "Client shall pay Service Provider [AMOUNT] [CURRENCY] according to the payment schedule outlined herein."
                        }
                    ],
                    "clauses": [
                        {
                            "id": "scope",
                            "title": "Scope of Services",
                            "content": "Detailed description of services to be provided."
                        },
                        {
                            "id": "payment",
                            "title": "Payment",
                            "content": "Payment terms and schedule."
                        }
                    ]
                }
            },
            {
                "id": "demo-template-employment-001",
                "title": "Employment Contract Template",
                "description": "Standard employment agreement template with comprehensive terms and conditions.",
                "type": "employment",
                "complexity": "medium",
                "industry": "All",
                "tags": ["employment", "hr", "job"],
                "icon": "users",
                "created_by": {
                    "id": "demo-hr-001",
                    "name": "Lisa Thompson"
                },
                "usage_count": 31,
                "rating": 4.9,
                "is_user_created": False,
                "workspace_id": "demo-workspace-hr",
                "content": {
                    "sections": [
                        {
                            "title": "Position Details",
                            "content": "Employee is hired for the position of [JOB_TITLE] reporting to [SUPERVISOR]."
                        },
                        {
                            "title": "Compensation",
                            "content": "Employee shall receive a salary of [SALARY] [CURRENCY] per [PAY_PERIOD]."
                        }
                    ],
                    "clauses": [
                        {
                            "id": "duties",
                            "title": "Duties and Responsibilities",
                            "content": "Employee shall perform duties as outlined in the job description."
                        },
                        {
                            "id": "compensation",
                            "title": "Compensation and Benefits",
                            "content": "Details of salary, benefits, and payment schedule."
                        }
                    ]
                }
            }
        ]

        created_templates = []
        for template_data in demo_templates:
            try:
                response = self.supabase.table("templates").insert(template_data).execute()
                if response.data:
                    created_templates.append(response.data[0])
                    print(f"   ✅ Created template: {template_data['title']}")
            except Exception as e:
                print(f"   ⚠️  Warning: Could not create template {template_data['title']}: {e}")

        return created_templates

    def seed_demo_contracts(self, workspaces: List[Dict[str, Any]], templates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create demo contracts with realistic data."""
        print("📋 Creating demo contracts...")

        demo_contracts = [
            {
                "id": "demo-contract-nda-001",
                "title": "NDA - TechCorp & DataSolutions Partnership",
                "type": "nda",
                "jurisdiction": "US",
                "effective_date": (datetime.now() - timedelta(days=30)).date().isoformat(),
                "expiry_date": (datetime.now() + timedelta(days=1065)).date().isoformat(),  # 3 years
                "description": "Non-disclosure agreement for potential partnership discussions between TechCorp and DataSolutions Inc.",
                "counterparty": "DataSolutions Inc.",
                "value": "N/A",
                "currency": "USD",
                "status": "active",
                "workspace_id": "demo-workspace-legal",
                "created_by": {
                    "id": "demo-lawyer-001",
                    "name": "Michael Chen"
                },
                "parties": [
                    {
                        "name": "TechCorp Inc.",
                        "type": "company",
                        "role": "disclosing_party",
                        "address": "123 Tech Street, San Francisco, CA 94105",
                        "representative": "Sarah Johnson",
                        "title": "Legal Operations Manager"
                    },
                    {
                        "name": "DataSolutions Inc.",
                        "type": "company",
                        "role": "receiving_party",
                        "address": "456 Data Avenue, Austin, TX 78701",
                        "representative": "John Smith",
                        "title": "Chief Technology Officer"
                    }
                ],
                "tags": ["partnership", "confidentiality", "technology"],
                "starred": True
            },
            {
                "id": "demo-contract-service-001",
                "title": "Software Development Services - Mobile App Project",
                "type": "service",
                "jurisdiction": "US",
                "effective_date": (datetime.now() - timedelta(days=15)).date().isoformat(),
                "expiry_date": (datetime.now() + timedelta(days=180)).date().isoformat(),  # 6 months
                "description": "Professional software development services for mobile application development project.",
                "counterparty": "Kim Consulting LLC",
                "value": "75000",
                "currency": "USD",
                "status": "active",
                "workspace_id": "demo-workspace-tech",
                "created_by": {
                    "id": "demo-admin-001",
                    "name": "Sarah Johnson"
                },
                "parties": [
                    {
                        "name": "TechCorp Inc.",
                        "type": "company",
                        "role": "client",
                        "address": "123 Tech Street, San Francisco, CA 94105",
                        "representative": "Emily Rodriguez",
                        "title": "Business Development Manager"
                    },
                    {
                        "name": "Kim Consulting LLC",
                        "type": "company",
                        "role": "service_provider",
                        "address": "789 Consultant Way, Denver, CO 80202",
                        "representative": "David Kim",
                        "title": "Principal Consultant"
                    }
                ],
                "tags": ["software", "development", "mobile", "consulting"],
                "starred": False
            },
            {
                "id": "demo-contract-employment-001",
                "title": "Employment Agreement - Senior Software Engineer",
                "type": "employment",
                "jurisdiction": "US",
                "effective_date": (datetime.now() + timedelta(days=14)).date().isoformat(),  # Future start date
                "description": "Employment agreement for Senior Software Engineer position in the Engineering department.",
                "counterparty": "Alex Thompson",
                "value": "120000",
                "currency": "USD",
                "status": "pending_signature",
                "workspace_id": "demo-workspace-hr",
                "created_by": {
                    "id": "demo-hr-001",
                    "name": "Lisa Thompson"
                },
                "parties": [
                    {
                        "name": "TechCorp Inc.",
                        "type": "company",
                        "role": "employer",
                        "address": "123 Tech Street, San Francisco, CA 94105",
                        "representative": "Lisa Thompson",
                        "title": "HR Director"
                    },
                    {
                        "name": "Alex Thompson",
                        "type": "individual",
                        "role": "employee",
                        "address": "321 Residential Lane, San Francisco, CA 94110",
                        "email": "<EMAIL>",
                        "phone": "******-0123"
                    }
                ],
                "tags": ["employment", "engineering", "full-time"],
                "starred": False
            }
        ]

        created_contracts = []
        for contract_data in demo_contracts:
            try:
                response = self.supabase.table("contracts").insert(contract_data).execute()
                if response.data:
                    created_contracts.append(response.data[0])
                    print(f"   ✅ Created contract: {contract_data['title']}")
            except Exception as e:
                print(f"   ⚠️  Warning: Could not create contract {contract_data['title']}: {e}")

        return created_contracts

    def run_full_seed(self, clear_existing: bool = False):
        """Run the complete seeding process."""
        print("🚀 Starting LegalAI V4 data seeding...")
        print("=" * 50)

        if clear_existing:
            self.clear_existing_data()
            print()

        # Step 1: Create default permissions
        permissions = self.seed_default_permissions()
        print()

        # Step 2: Create users
        users = self.seed_demo_users()
        print()

        # Step 3: Create workspaces
        workspaces = self.seed_demo_workspaces(users)
        print()

        # Step 4: Create default roles for each workspace
        roles = self.seed_default_roles(workspaces)
        print()

        # Step 5: Add users to workspaces
        self.seed_workspace_members(users, workspaces)
        print()

        # Step 6: Create templates
        templates = self.seed_demo_templates(workspaces)
        print()

        # Step 7: Create contracts
        contracts = self.seed_demo_contracts(workspaces, templates)
        print()

        print("=" * 50)
        print("✅ Data seeding completed successfully!")
        print(f"   🔐 Created {len(permissions)} system permissions")
        print(f"   👥 Created {len(users)} demo users")
        print(f"   🏢 Created {len(workspaces)} demo workspaces")
        print(f"   👤 Created {len(roles)} workspace roles")
        print(f"   📄 Created {len(templates)} demo templates")
        print(f"   📋 Created {len(contracts)} demo contracts")
        print()
        print("🎉 Your LegalAI V4 demo environment is ready!")
        print("   You can now log in and explore the features with realistic data.")


def main():
    """Main function to run the seeding script."""
    import argparse

    parser = argparse.ArgumentParser(description="Seed LegalAI V4 database with demo data")
    parser.add_argument("--clear", action="store_true", help="Clear existing demo data before seeding")
    parser.add_argument("--env-file", type=str, help="Path to .env file (optional)")

    args = parser.parse_args()

    # Load environment variables from .env file if specified
    if args.env_file:
        from dotenv import load_dotenv
        load_dotenv(args.env_file)

    try:
        seeder = DataSeeder()
        seeder.run_full_seed(clear_existing=args.clear)
    except Exception as e:
        print(f"❌ Error during seeding: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
